import { Meteor } from 'meteor/meteor';
import { HTTP } from 'meteor/http';
import { check } from 'meteor/check';

/**
 * Server method to convert ZPL to PDF using the Labelary API.
 * Returns a base64-encoded PDF string so the client can trigger a download
 * without running into CORS issues.
 */
Meteor.methods({
  'labelary.convertZplToPdf'(zplString) {
    check(zplString, String);

    // Allow other method calls from same client to run without waiting.
    this.unblock();

    const url = 'https://api.labelary.com/v1/printers/8dpmm/labels/4x6/0/';

    try {
      const { content } = HTTP.post(url, {
        headers: {
          Accept: 'application/pdf',
        },
        content: zplString,
        // Ensure we get raw binary back
        npmRequestOptions: {
          encoding: null,
        },
      });

      // content is a Buffer thanks to encoding:null
      const base64Pdf = Buffer.from(content).toString('base64');
      return base64Pdf;
    } catch (err) {
      // Re-throw as Meteor error for client handling
      throw new Meteor.Error('labelary-error', err.message);
    }
  },
});
