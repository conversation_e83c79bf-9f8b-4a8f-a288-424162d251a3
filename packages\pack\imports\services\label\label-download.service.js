import { $ } from 'meteor/jquery';
import { Meteor } from 'meteor/meteor';
import { ZplLabelService } from '../../ui/zpl/label';

/**
 * Service for handling label download and print preview operations
 */
export const LabelDownloadService = {
  /**
   * Downloads a label PDF for a given item
   * @param {Object} item - The item for which to generate and download a label
   * @returns {Object} - A notification element that can be used to update the UI
   */
  downloadLabelPdf(item) {
    // Show loading notification
    const notification = $('<div class="ui small message">')
      .html('<i class="notched circle loading icon"></i> Preparing label...')
      .css({
        position: "fixed",
        bottom: "20px",
        right: "20px",
        zIndex: 9999,
        padding: "10px 15px",
        boxShadow: "0 0 10px rgba(0,0,0,0.2)",
      })
      .appendTo("body");
    
    try {
      // Generate the ZPL data for this item
      const zplData = ZplLabelService.createLabel(item);
      
      // Call server method to convert ZPL to PDF
      Meteor.call('labelary.convertZplToPdf', zplData, (error, base64Pdf) => {
        if (error) {
          console.error('Error converting ZPL to PDF:', error);
          notification.html('<i class="exclamation triangle icon"></i> Error downloading label PDF')
            .addClass('negative')
            .delay(3000)
            .fadeOut(400, function() { $(this).remove(); });
          return;
        }

        // Decode base64 PDF
        const byteCharacters = atob(base64Pdf);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: 'application/pdf' });
        const url = window.URL.createObjectURL(blob);

        // Trigger download
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `Label-${item.identifier || item.receiptNo || 'download'}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        setTimeout(() => { document.body.removeChild(a); }, 100);

        notification.html('<i class="check icon"></i> Label PDF downloaded!')
          .removeClass('loading')
          .delay(2000)
          .fadeOut(400, function() { $(this).remove(); });
      });
      
      return notification;
    } catch (e) {
      console.error("Error preparing label for download:", e);
      notification.html('<i class="exclamation triangle icon"></i> Error downloading label')
        .addClass("negative")
        .delay(3000)
        .fadeOut(400, function() { $(this).remove(); });
      
      return notification;
    }
  }
};
