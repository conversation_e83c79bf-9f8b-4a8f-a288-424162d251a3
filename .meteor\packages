# Meteor packages used by this project, one per line.
# Check this file (and the other files in this directory) into your repository.
#
# 'meteor add' and 'meteor remove' will edit this file for you,
# but you can also edit it by hand.

meteor-base@1.5.1             # Packages every Meteor app needs to have
mobile-experience@1.1.0       # Packages for a great mobile UX
mongo@1.14.6                   # The database Meteor supports right now
blaze-html-templates@1.2.1    # Compile .html files into Meteor Blaze views
reactive-var@1.0.11            # Reactive variable for tracker
tracker@1.2.0                 # Meteor's client-side reactive programming library
es5-shim@4.8.0                # ECMAScript 5 compatibility for older browsers
ecmascript@0.16.1              # Enable ECMAScript2015+ syntax in app code
shell-server@0.5.0            # Server-side component of the `meteor shell` command
ostrio:flow-router-extra@3.9.0      # FlowRouter is a very simple router for Meteor
kadira:blaze-layout     # Layout manager for blaze (works well with FlowRouter)
less@2.8.0                    # Leaner CSS language
meteortesting:mocha # A package for writing and running your meteor app and package tests with mocha
johan<PERSON>:publication-collector  # Test a Meteor publication by collecting its output
semantic:ui@2.2.6_5
juliancwirko:postcss
aldeed:collection2-core
check@1.3.1
pack
receipt
accounts-password@2.2.0
standard-minifier-js@2.8.0
underscore@1.0.10
mdg:meteor-apm-agent@3.5.1
msavin:mongol@10.0.1
http@1.4.4
